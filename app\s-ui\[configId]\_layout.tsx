import { Drawer } from 'expo-router/drawer';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { LayoutGrid, Settings, Undo2 } from 'lucide-react-native';
import React from 'react';
import { DrawerContentScrollView, DrawerItemList, DrawerItem } from '@react-navigation/drawer';
import { View, StyleSheet } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useAppStore } from '~/lib/store';

function CustomDrawerContent(props: any) {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const handleBackToHome = () => {
    router.replace('/');
  };

  return (
    <View style={[styles.drawerContainer, { backgroundColor }]}>
      <DrawerContentScrollView {...props} style={styles.scrollView}>
        <DrawerItemList {...props} />
      </DrawerContentScrollView>

      {/* Footer 区域 */}
      <View style={[styles.footer, { borderTopColor: borderColor }]}>
        <DrawerItem
          label={t('sui.drawer.backToHome')}
          onPress={handleBackToHome}
          pressOpacity={0.5}
          icon={({ color, size }) => <Undo2 color={color} size={size} />}
          labelStyle={{ color: textColor, fontSize: 16, fontWeight: '500' }}
          activeTintColor={textColor}
          inactiveTintColor={textColor + '80'}
        />
      </View>
    </View>
  );
}

export default function SUIConfigLayout() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { configs } = useAppStore()

  return (
    <>
      <Drawer
        drawerContent={(props) => <CustomDrawerContent {...props} />}
        screenOptions={{
          drawerStyle: {
            backgroundColor,
            borderRightColor: borderColor,
            borderRightWidth: 1,
          },
          drawerLabelStyle: {
            color: textColor,
            fontSize: 16,
            fontWeight: '500',
          },
          drawerActiveTintColor: textColor,
          drawerInactiveTintColor: textColor + '80',
          headerTintColor: textColor,
          headerTitleStyle: {
            color: textColor,
            fontSize: 18,
            fontWeight: '600',
          },
        }}
      >
        <Drawer.Screen
          name="index"
          options={{
            drawerLabel: t('sui.drawer.overview'),
            title: configs.find(c => c.id === configId)?.name,
            drawerIcon: ({ color, size }) => (
              <LayoutGrid color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
        <Drawer.Screen
          name="settings"
          options={{
            drawerLabel: t('sui.drawer.settings'),
            title: t('sui.settings.title'),
            drawerIcon: ({ color, size }) => (
              <Settings color={color} size={size} />
            ),
          }}
          initialParams={{configId}}
        />
    </Drawer>
  );
}

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  footer: {
    borderTopWidth: 1,
    paddingTop: 8,
    paddingBottom: 24,
  },
});
