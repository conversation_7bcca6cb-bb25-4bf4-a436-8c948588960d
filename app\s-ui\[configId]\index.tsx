import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { getUsageColor, monitorCardStyles } from '~/components/styles/MonitorCardStyles';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { SUIConfig, MonitoringStatus } from '@/lib/types';
import { getSUIServerStatus } from '@/panels/s-ui/utils';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { RefreshCw, ArrowUp, ArrowDown } from 'lucide-react-native';
import React, { useCallback, useMemo, useState } from 'react';
import { ScrollView, StyleSheet, View, Alert, TouchableOpacity, SafeAreaView } from 'react-native';
import { Pie, <PERSON><PERSON><PERSON> } from 'victory-native';

// 动画饼图组件
const AnimatedPieChart = ({ data }: { data: any[] }) => {
  return (
    <PolarChart data={data} labelKey="label" valueKey="value" colorKey="color">
      <Pie.Chart innerRadius={30}>
        {({ slice }) => (
          <Pie.Slice animate={{ type: 'timing', duration: 300 }} />
        )}
      </Pie.Chart>
    </PolarChart>
  );
};

export default function SUIOverviewScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const { configs, getMonitoringStatus, setMonitoringStatus, getProxyServer } = useAppStore();

  // 直接从configs中计算config，无需useState和useEffect
  const config = useMemo(() => {
    if (!configId) return null;
    return configs.find(c => c.id === configId) as SUIConfig || null;
  }, [configId, configs]);

  const [isRefreshing, setIsRefreshing] = useState(false);

  // 获取监控状态
  const status = getMonitoringStatus(configId || '');

  // 刷新数据
  const refreshData = useCallback(async () => {
    if (!config || isRefreshing) return;

    setIsRefreshing(true);
    try {
      const serverStatus = await getSUIServerStatus(config);
      const newStatus: MonitoringStatus = {
        isOnline: serverStatus !== null,
        lastUpdate: Date.now(),
        serverStatus: serverStatus || undefined,
        failureCount: serverStatus ? 0 : (status?.failureCount || 0) + 1,
      };

      setMonitoringStatus(config.id, newStatus);
    } catch (error) {
      console.error('Failed to refresh S-UI data:', error);
      Alert.alert(t('common.error'), t('sui.overview.refreshFailed'));
    } finally {
      setIsRefreshing(false);
    }
  }, [config, isRefreshing, status?.failureCount, setMonitoringStatus, t]);

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      refreshData();
    }, [refreshData])
  );

  // 安全数字转换
  const safeNumber = (value: any): number => {
    if (typeof value === 'number' && !isNaN(value)) return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  };

  // 格式化字节
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化速度
  const formatSpeed = (bytesPerSecond: number): string => {
    return formatBytes(bytesPerSecond) + '/s';
  };

  // 格式化运行时间
  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // 获取主机名
  const getHostname = (): string => {
    if (!config?.url) return '';
    try {
      return new URL(`${config.protocol}://${config.url}`).hostname;
    } catch {
      return config.url.split('/')[0];
    }
  };

  if (!config) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: textColor + '60' }]}>
            {t('common.configNotFound')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // 兼容不同s-ui返回结构的辅助变量
  const anyStatus = status?.serverStatus as any | undefined;
  const cpuCoresDisplay = status?.serverStatus ? safeNumber(anyStatus?.sys?.cpuCount ?? (status.serverStatus as any).cpuCores ?? 0) : 0;
  const netUpSpeed = status?.serverStatus ? safeNumber(anyStatus?.net?.psent ?? anyStatus?.netIO?.up ?? 0) : 0;
  const netUpTotal = status?.serverStatus ? safeNumber(anyStatus?.net?.sent ?? anyStatus?.netTraffic?.sent ?? 0) : 0;
  const netDownSpeed = status?.serverStatus ? safeNumber(anyStatus?.net?.precv ?? anyStatus?.netIO?.down ?? 0) : 0;
  const netDownTotal = status?.serverStatus ? safeNumber(anyStatus?.net?.recv ?? anyStatus?.netTraffic?.recv ?? 0) : 0;

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        {!status || !status.isOnline ? (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: textColor + '60' }]}>
              No Data
            </Text>
            <Text style={[styles.noDataSubtext, { color: textColor + '40' }]}>
              {status?.isOnline === false
                ? t('sui.overview.serverOffline')
                : t('sui.overview.gettingData')}
            </Text>
          </View>
        ) : (
          <>
            {/* 服务器信息卡片 */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: textColor }]}>
                  {t('sui.overview.serverInfo')}
                </Text>
                <TouchableOpacity
                  onPress={refreshData}
                  disabled={isRefreshing}
                  style={[styles.refreshButton, { borderColor }]}
                >
                  <RefreshCw
                    size={16}
                    color={textColor}
                    style={isRefreshing ? styles.spinning : undefined}
                  />
                </TouchableOpacity>
              </View>

              <View style={[styles.card, { backgroundColor, borderColor }]}>
                {/* 标题和状态 */}
                <View style={monitorCardStyles.header}>
                  <View style={monitorCardStyles.titleContainer}>
                    <View style={monitorCardStyles.titleRow}>
                      <Text style={[monitorCardStyles.title, { color: textColor }]}>{config.name}</Text>
                      <Badge variant="secondary" style={monitorCardStyles.ipBadge}>
                        <Text style={monitorCardStyles.ipBadgeText}>{getHostname()}</Text>
                      </Badge>
                    </View>
                  </View>
                  <Badge
                    variant={status.isOnline ? "default" : "destructive"}
                    style={monitorCardStyles.statusBadge}
                  >
                    <Text style={monitorCardStyles.badgeText}>
                      {status.isOnline
                        ? status.serverStatus ? formatUptime(status.serverStatus.uptime) : 'Online'
                        : 'Offline'
                      }
                    </Text>
                  </Badge>
                </View>

                {/* 系统资源使用率圆环图 */}
                <View style={monitorCardStyles.chartsContainer}>
                  <View style={monitorCardStyles.chartItem}>
                    <View style={monitorCardStyles.pieContainer}>
                      <AnimatedPieChart
                        data={status.serverStatus ? (() => {
                          const cpu = safeNumber(status.serverStatus.cpu);
                          const cpuPercentage = cpu <= 1 ? cpu * 100 : cpu;
                          return [
                            {
                              label: 'Used',
                              value: cpuPercentage,
                              color: getUsageColor(cpuPercentage)
                            },
                            {
                              label: 'Free',
                              value: 100 - cpuPercentage,
                              color: '#e5e7eb'
                            }
                          ];
                        })() : [
                          {
                            label: 'No Data',
                            value: 100,
                            color: '#f3f4f6'
                          }
                        ]}
                      />
                      <View style={monitorCardStyles.pieCenter}>
                        <Text style={[monitorCardStyles.pieCenterValue, { color: textColor }]}>
                          {status.serverStatus ? `${Math.round(safeNumber(status.serverStatus.cpu) <= 1 ? safeNumber(status.serverStatus.cpu) * 100 : safeNumber(status.serverStatus.cpu))}%` : '--'}
                        </Text>
                      </View>
                    </View>
                    <Text style={[monitorCardStyles.chartLabel, { color: textColor }]}>
                      CPU ({cpuCoresDisplay} {t('sui.overview.cores')})
                    </Text>
                  </View>

                  <View style={monitorCardStyles.chartItem}>
                    <View style={monitorCardStyles.pieContainer}>
                      <AnimatedPieChart
                        data={status.serverStatus ? (() => {
                          const memCurrent = safeNumber(status.serverStatus.mem.current);
                          const memTotal = safeNumber(status.serverStatus.mem.total);
                          const memPercentage = memTotal > 0 ? (memCurrent / memTotal) * 100 : 0;
                          return [
                            {
                              label: 'Used',
                              value: memPercentage,
                              color: getUsageColor(memPercentage)
                            },
                            {
                              label: 'Free',
                              value: 100 - memPercentage,
                              color: '#e5e7eb'
                            }
                          ];
                        })() : [
                          {
                            label: 'No Data',
                            value: 100,
                            color: '#f3f4f6'
                          }
                        ]}
                      />
                      <View style={monitorCardStyles.pieCenter}>
                        <Text style={[monitorCardStyles.pieCenterValue, { color: textColor }]}>
                          {status.serverStatus ? `${Math.round((safeNumber(status.serverStatus.mem.current) / safeNumber(status.serverStatus.mem.total)) * 100)}%` : '--'}
                        </Text>
                      </View>
                    </View>
                    <Text style={[monitorCardStyles.chartLabel, { color: textColor }]}>
                      {t('sui.overview.memory')} ({status.serverStatus ? formatBytes(safeNumber(status.serverStatus.mem.total)) : '--'})
                    </Text>
                  </View>

                  <View style={monitorCardStyles.chartItem}>
                    <View style={monitorCardStyles.pieContainer}>
                      <AnimatedPieChart
                        data={status.serverStatus ? (() => {
                          const diskCurrent = safeNumber(status.serverStatus.disk.current);
                          const diskTotal = safeNumber(status.serverStatus.disk.total);
                          const diskPercentage = diskTotal > 0 ? (diskCurrent / diskTotal) * 100 : 0;
                          return [
                            {
                              label: 'Used',
                              value: diskPercentage,
                              color: getUsageColor(diskPercentage)
                            },
                            {
                              label: 'Free',
                              value: 100 - diskPercentage,
                              color: '#e5e7eb'
                            }
                          ];
                        })() : [
                          {
                            label: 'No Data',
                            value: 100,
                            color: '#f3f4f6'
                          }
                        ]}
                      />
                      <View style={monitorCardStyles.pieCenter}>
                        <Text style={[monitorCardStyles.pieCenterValue, { color: textColor }]}>
                          {status.serverStatus ? `${Math.round((safeNumber(status.serverStatus.disk.current) / safeNumber(status.serverStatus.disk.total)) * 100)}%` : '--'}
                        </Text>
                      </View>
                    </View>
                    <Text style={[monitorCardStyles.chartLabel, { color: textColor }]}>
                      {t('sui.overview.storage')} ({status.serverStatus ? formatBytes(safeNumber(status.serverStatus.disk.total)) : '--'})
                    </Text>
                  </View>
                </View>

                {/* 网络流量 */}
                <View style={monitorCardStyles.networkContainer}>
                  <View style={monitorCardStyles.networkItem}>
                    <ArrowUp size={16} color="#10b981" />
                    <Text style={[monitorCardStyles.networkSpeed, { color: textColor }]}>
                      {formatSpeed(netUpSpeed)}
                    </Text>
                    <Text style={[monitorCardStyles.networkTotal, { color: textColor + '80' }]}>
                      {formatBytes(netUpTotal)}
                    </Text>
                  </View>
                  <View style={monitorCardStyles.networkItem}>
                    <ArrowDown size={16} color="#3b82f6" />
                    <Text style={[monitorCardStyles.networkSpeed, { color: textColor }]}>
                      {formatSpeed(netDownSpeed)}
                    </Text>
                    <Text style={[monitorCardStyles.networkTotal, { color: textColor + '80' }]}>
                      {formatBytes(netDownTotal)}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  refreshButton: {
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  spinning: {
    transform: [{ rotate: '360deg' }],
  },
  card: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  noDataText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});
