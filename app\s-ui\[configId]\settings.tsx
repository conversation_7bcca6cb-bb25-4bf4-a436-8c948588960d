import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { SUIConfig } from '@/lib/types';
import { router, useLocalSearchParams } from 'expo-router';
import { Settings, Edit, Trash2 } from 'lucide-react-native';
import React, { useMemo } from 'react';
import { ScrollView, StyleSheet, View, Alert, SafeAreaView } from 'react-native';

export default function SUISettingsScreen() {
  const { configId } = useLocalSearchParams<{ configId: string }>();
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const { configs, removeConfig } = useAppStore();

  // 直接从configs中计算config
  const config = useMemo(() => {
    if (!configId) return null;
    return configs.find(c => c.id === configId) as SUIConfig || null;
  }, [configId, configs]);

  // 编辑配置
  const handleEditConfig = () => {
    if (!config) return;
    router.push({
      pathname: '/config-form/s-ui',
      params: { configId: config.id }
    });
  };

  // 删除配置
  const handleDeleteConfig = () => {
    if (!config) return;

    Alert.alert(
      t('sui.settings.deleteConfig'),
      t('sui.settings.deleteConfigConfirm', { name: config.name }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            removeConfig(config.id);
            router.replace('/');
          },
        },
      ]
    );
  };

  if (!config) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: textColor + '60' }]}>
            {t('common.configNotFound')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        {/* 配置信息 */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('sui.settings.configInfo')}
          </Text>
          
          <View style={[styles.card, { backgroundColor, borderColor }]}>
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: textColor + '80' }]}>
                {t('common.name')}
              </Text>
              <Text style={[styles.infoValue, { color: textColor }]}>
                {config.name}
              </Text>
            </View>
            
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: textColor + '80' }]}>
                {t('common.url')}
              </Text>
              <Text style={[styles.infoValue, { color: textColor }]}>
                {config.protocol}://{config.url}
              </Text>
            </View>
            
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: textColor + '80' }]}>
                {t('sui.settings.apiKey')}
              </Text>
              <Text style={[styles.infoValue, { color: textColor }]}>
                {config.api ? '••••••••' : t('common.notSet')}
              </Text>
            </View>
            
            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, { color: textColor + '80' }]}>
                {t('common.certificate')}
              </Text>
              <Text style={[styles.infoValue, { color: textColor }]}>
                {config.cert ? t('common.configured') : t('common.notSet')}
              </Text>
            </View>
          </View>
        </View>

        {/* 操作按钮 */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('sui.settings.actions')}
          </Text>
          
          <View style={styles.buttonContainer}>
            <Button
              onPress={handleEditConfig}
              style={[styles.actionButton, { borderColor }]}
              variant="outline"
            >
              <Edit size={16} color={textColor} style={styles.buttonIcon} />
              <Text style={[styles.buttonText, { color: textColor }]}>
                {t('sui.settings.editConfig')}
              </Text>
            </Button>
            
            <Button
              onPress={handleDeleteConfig}
              style={[styles.actionButton, styles.deleteButton]}
              variant="outline"
            >
              <Trash2 size={16} color="#ef4444" style={styles.buttonIcon} />
              <Text style={[styles.buttonText, { color: '#ef4444' }]}>
                {t('sui.settings.deleteConfig')}
              </Text>
            </Button>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  card: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    flex: 2,
    textAlign: 'right',
  },
  buttonContainer: {
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  deleteButton: {
    borderColor: '#ef4444',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  noDataText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
  },
});
